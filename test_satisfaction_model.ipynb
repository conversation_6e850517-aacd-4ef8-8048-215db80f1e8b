{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Customer Satisfaction Model Testing\n", "\n", "This notebook provides comprehensive testing functionality for your customer satisfaction classifier trained on facial expressions.\n", "\n", "## Model Overview\n", "- **Input**: 48x48 grayscale facial images\n", "- **Output**: 3 classes (Satisfied, Neutral, Unsatisfied)\n", "- **Architecture**: CNN trained on FER2013 dataset\n", "- **Test Accuracy**: ~78.6%"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import os\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import cv2\n", "from pathlib import Path\n", "import tensorflow as tf\n", "from tensorflow.keras.models import load_model\n", "from IPython.display import display, Image, clear_output\n", "import ipywidgets as widgets\n", "from ipywidgets import interact, interactive, fixed, interact_manual\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set matplotlib style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")\n", "print(f\"TensorFlow version: {tf.__version__}\")\n", "print(f\"OpenCV version: {cv2.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON> the Trained Model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Model configuration\n", "MODEL_PATH = 'satisfaction_model_best.h5'\n", "IMAGE_SIZE = (48, 48)\n", "\n", "# Class labels and colors for visualization\n", "SATISFACTION_LABELS = {\n", "    0: 'Satisfied',\n", "    1: 'Neutral', \n", "    2: 'Unsatisfied'\n", "}\n", "\n", "COLORS = {\n", "    'Satisfied': '#2ecc71',      # Green\n", "    'Neutral': '#f39c12',        # Orange\n", "    'Unsatisfied': '#e74c3c'     # Red\n", "}\n", "\n", "# Load the model\n", "try:\n", "    if os.path.exists(MODEL_PATH):\n", "        print(f\"Loading model from {MODEL_PATH}...\")\n", "        model = load_model(MODEL_PATH)\n", "        print(\"✅ Model loaded successfully!\")\n", "        \n", "        # Display model summary\n", "        print(\"\\nModel Architecture:\")\n", "        model.summary()\n", "    else:\n", "        print(f\"❌ Model file not found: {MODEL_PATH}\")\n", "        print(\"Please make sure the model file is in the current directory.\")\n", "        model = None\n", "except Exception as e:\n", "    print(f\"❌ Error loading model: {e}\")\n", "    model = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Image Preprocessing Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def preprocess_image(image_input):\n", "    \"\"\"\n", "    Preprocess image for model prediction\n", "    \n", "    Args:\n", "        image_input: Can be image path (str), numpy array, or PIL Image\n", "        \n", "    Returns:\n", "        Preprocessed image ready for model input\n", "    \"\"\"\n", "    # Load image if it's a path\n", "    if isinstance(image_input, (str, Path)):\n", "        if not os.path.exists(image_input):\n", "            raise FileNotFoundError(f\"Image file not found: {image_input}\")\n", "        image = cv2.imread(str(image_input))\n", "        if image is None:\n", "            raise ValueError(f\"Could not load image: {image_input}\")\n", "        # Convert BGR to RGB\n", "        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)\n", "    else:\n", "        image = image_input.copy()\n", "    \n", "    # Convert to grayscale if needed\n", "    if len(image.shape) == 3:\n", "        image = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)\n", "    \n", "    # Resize to model input size (48x48)\n", "    image = cv2.resize(image, IMAGE_SIZE)\n", "    \n", "    # Normalize pixel values to [0, 1]\n", "    image = image.astype(np.float32) / 255.0\n", "    \n", "    # Add batch and channel dimensions\n", "    image = np.expand_dims(image, axis=0)  # Batch dimension\n", "    image = np.expand_dims(image, axis=-1)  # Channel dimension\n", "    \n", "    return image\n", "\n", "def load_and_display_image(image_path, title=\"Image\"):\n", "    \"\"\"\n", "    Load and display an image\n", "    \"\"\"\n", "    try:\n", "        image = cv2.imread(image_path)\n", "        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)\n", "        \n", "        plt.figure(figsize=(6, 6))\n", "        plt.imshow(image)\n", "        plt.title(title)\n", "        plt.axis('off')\n", "        plt.show()\n", "        \n", "        return image\n", "    except Exception as e:\n", "        print(f\"Error loading image: {e}\")\n", "        return None\n", "\n", "print(\"✅ Preprocessing functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Prediction Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def predict_satisfaction(image_input, return_probabilities=True, verbose=True):\n", "    \"\"\"\n", "    Predict customer satisfaction from facial expression\n", "    \n", "    Args:\n", "        image_input: Image path, numpy array, or PIL Image\n", "        return_probabilities: Whether to return class probabilities\n", "        verbose: Whether to print results\n", "        \n", "    Returns:\n", "        Dictionary with prediction results\n", "    \"\"\"\n", "    if model is None:\n", "        print(\"❌ Model not loaded. Please load the model first.\")\n", "        return None\n", "    \n", "    try:\n", "        # Preprocess image\n", "        processed_image = preprocess_image(image_input)\n", "        \n", "        # Make prediction\n", "        predictions = model.predict(processed_image, verbose=0)\n", "        \n", "        # Get results\n", "        predicted_class = np.argmax(predictions[0])\n", "        confidence = predictions[0][predicted_class]\n", "        predicted_label = SATISFACTION_LABELS[predicted_class]\n", "        \n", "        result = {\n", "            'predicted_class': predicted_class,\n", "            'predicted_label': predicted_label,\n", "            'confidence': float(confidence),\n", "            'color': COLORS[predicted_label]\n", "        }\n", "        \n", "        if return_probabilities:\n", "            result['probabilities'] = {\n", "                SATISFACTION_LABELS[i]: float(predictions[0][i]) \n", "                for i in range(len(SATISFACTION_LABELS))\n", "            }\n", "        \n", "        if verbose:\n", "            print(f\"🎯 Prediction: {predicted_label}\")\n", "            print(f\"📊 Confidence: {confidence:.3f}\")\n", "            if return_probabilities:\n", "                print(\"📈 All probabilities:\")\n", "                for label, prob in result['probabilities'].items():\n", "                    print(f\"   {label}: {prob:.3f}\")\n", "        \n", "        return result\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error predicting image: {e}\")\n", "        return None\n", "\n", "def visualize_prediction(image_path, save_path=None):\n", "    \"\"\"\n", "    Visualize prediction results with original image and probability bars\n", "    \"\"\"\n", "    try:\n", "        # Load original image\n", "        original_image = cv2.imread(image_path)\n", "        if original_image is None:\n", "            print(f\"❌ Could not load image: {image_path}\")\n", "            return\n", "        \n", "        original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)\n", "        \n", "        # Get prediction\n", "        result = predict_satisfaction(image_path, return_probabilities=True, verbose=False)\n", "        if not result:\n", "            return\n", "        \n", "        # Create visualization\n", "        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "        \n", "        # Show original image\n", "        ax1.imshow(original_image)\n", "        ax1.set_title(f\"Original Image\\n{os.path.basename(image_path)}\", fontsize=14)\n", "        ax1.axis('off')\n", "        \n", "        # Show prediction results\n", "        labels = list(result['probabilities'].keys())\n", "        probabilities = list(result['probabilities'].values())\n", "        colors = [COLORS[label] for label in labels]\n", "        \n", "        bars = ax2.bar(labels, probabilities, color=colors, alpha=0.8)\n", "        ax2.set_title(f\"Prediction: {result['predicted_label']}\\n\"\n", "                     f\"Confidence: {result['confidence']:.3f}\", fontsize=14)\n", "        ax2.set_ylabel('Probability', fontsize=12)\n", "        ax2.set_ylim(0, 1)\n", "        ax2.grid(True, alpha=0.3)\n", "        \n", "        # Add probability values on bars\n", "        for bar, prob in zip(bars, probabilities):\n", "            height = bar.get_height()\n", "            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.02,\n", "                    f'{prob:.3f}', ha='center', va='bottom', fontweight='bold')\n", "        \n", "        plt.tight_layout()\n", "        \n", "        if save_path:\n", "            plt.savefig(save_path, dpi=150, bbox_inches='tight')\n", "            print(f\"💾 Visualization saved to: {save_path}\")\n", "        \n", "        plt.show()\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error visualizing prediction: {e}\")\n", "\n", "print(\"✅ Prediction functions defined!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Test Single Image\n", "\n", "Upload or specify the path to an image you want to test:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test a single image\n", "# Replace 'your_image.jpg' with the path to your image\n", "IMAGE_PATH = \"your_image.jpg\"  # Change this to your image path\n", "\n", "# Check if image exists\n", "if os.path.exists(IMAGE_PATH):\n", "    print(f\"📸 Testing image: {IMAGE_PATH}\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Display original image\n", "    original_image = load_and_display_image(IMAGE_PATH, \"Original Image\")\n", "    \n", "    if original_image is not None:\n", "        # Make prediction\n", "        result = predict_satisfaction(IMAGE_PATH)\n", "        \n", "        # Visualize results\n", "        print(\"\\n📊 Detailed Visualization:\")\n", "        visualize_prediction(IMAGE_PATH)\n", "else:\n", "    print(f\"❌ Image not found: {IMAGE_PATH}\")\n", "    print(\"Please update IMAGE_PATH with a valid image file path.\")\n", "    print(\"\\nSupported formats: .jpg, .jpeg, .png, .bmp, .tiff\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Interactive Image Upload Widget\n", "\n", "Use this widget to upload and test images directly in the notebook:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# File upload widget\n", "from ipywidgets import FileUpload, Output, VBox, HBox, Button\n", "from IPython.display import display, clear_output\n", "import io\n", "from PIL import Image as PILImage\n", "\n", "# Create upload widget\n", "upload_widget = FileUpload(\n", "    accept='.jpg,.jpeg,.png,.bmp,.tiff',  # Restrict to image files\n", "    multiple=False,  # Single file upload\n", "    description='Upload Image'\n", ")\n", "\n", "# Output widget for results\n", "output_widget = Output()\n", "\n", "def process_uploaded_image(change):\n", "    \"\"\"Process uploaded image and show prediction\"\"\"\n", "    with output_widget:\n", "        clear_output(wait=True)\n", "        \n", "        if upload_widget.value:\n", "            # Get the uploaded file\n", "            uploaded_file = list(upload_widget.value.values())[0]\n", "            \n", "            try:\n", "                # Convert to PIL Image\n", "                image = PILImage.open(io.BytesIO(uploaded_file['content']))\n", "                \n", "                # Convert to numpy array\n", "                image_array = np.array(image)\n", "                \n", "                # Display original image\n", "                plt.figure(figsize=(8, 6))\n", "                plt.imshow(image_array)\n", "                plt.title(f\"Uploaded Image: {uploaded_file['metadata']['name']}\")\n", "                plt.axis('off')\n", "                plt.show()\n", "                \n", "                # Make prediction\n", "                print(\"\\n🔍 Analyzing image...\")\n", "                result = predict_satisfaction(image_array)\n", "                \n", "                if result:\n", "                    # Create probability visualization\n", "                    fig, ax = plt.subplots(figsize=(10, 6))\n", "                    \n", "                    labels = list(result['probabilities'].keys())\n", "                    probabilities = list(result['probabilities'].values())\n", "                    colors = [COLORS[label] for label in labels]\n", "                    \n", "                    bars = ax.bar(labels, probabilities, color=colors, alpha=0.8)\n", "                    ax.set_title(f\"Prediction: {result['predicted_label']} (Confidence: {result['confidence']:.3f})\", \n", "                                fontsize=16, fontweight='bold')\n", "                    ax.set_ylabel('Probability', fontsize=12)\n", "                    ax.set_ylim(0, 1)\n", "                    ax.grid(True, alpha=0.3)\n", "                    \n", "                    # Add values on bars\n", "                    for bar, prob in zip(bars, probabilities):\n", "                        height = bar.get_height()\n", "                        ax.text(bar.get_x() + bar.get_width()/2., height + 0.02,\n", "                               f'{prob:.3f}', ha='center', va='bottom', fontweight='bold')\n", "                    \n", "                    plt.tight_layout()\n", "                    plt.show()\n", "                    \n", "            except Exception as e:\n", "                print(f\"❌ Error processing uploaded image: {e}\")\n", "\n", "# Attach the event handler\n", "upload_widget.observe(process_uploaded_image, names='value')\n", "\n", "# Display the widget\n", "display(VBox([upload_widget, output_widget]))\n", "\n", "print(\"📤 Upload an image file to test the model!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON> Testing - Multiple Images\n", "\n", "Test multiple images from a folder:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_image_folder(folder_path, max_images=10, show_images=True):\n", "    \"\"\"\n", "    Test all images in a folder\n", "    \n", "    Args:\n", "        folder_path: Path to folder containing images\n", "        max_images: Maximum number of images to process\n", "        show_images: Whether to display the images\n", "    \"\"\"\n", "    if not os.path.exists(folder_path):\n", "        print(f\"❌ Folder not found: {folder_path}\")\n", "        return\n", "    \n", "    # Get all image files\n", "    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}\n", "    image_files = []\n", "    \n", "    for ext in image_extensions:\n", "        image_files.extend(Path(folder_path).glob(f'*{ext}'))\n", "        image_files.extend(Path(folder_path).glob(f'*{ext.upper()}'))\n", "    \n", "    if not image_files:\n", "        print(f\"❌ No image files found in {folder_path}\")\n", "        return\n", "    \n", "    # Limit number of images\n", "    image_files = image_files[:max_images]\n", "    \n", "    print(f\"📁 Testing {len(image_files)} images from {folder_path}\")\n", "    print(\"=\" * 60)\n", "    \n", "    results = []\n", "    \n", "    for i, image_path in enumerate(image_files):\n", "        print(f\"\\n📸 Image {i+1}/{len(image_files)}: {image_path.name}\")\n", "        \n", "        try:\n", "            # Make prediction\n", "            result = predict_satisfaction(str(image_path), verbose=False)\n", "            \n", "            if result:\n", "                result['image_path'] = str(image_path)\n", "                result['image_name'] = image_path.name\n", "                results.append(result)\n", "                \n", "                print(f\"   🎯 {result['predicted_label']} (Confidence: {result['confidence']:.3f})\")\n", "                \n", "                # Show image if requested\n", "                if show_images:\n", "                    image = cv2.imread(str(image_path))\n", "                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)\n", "                    \n", "                    plt.figure(figsize=(6, 4))\n", "                    plt.imshow(image)\n", "                    plt.title(f\"{image_path.name}\\n{result['predicted_label']} ({result['confidence']:.3f})\")\n", "                    plt.axis('off')\n", "                    plt.show()\n", "            else:\n", "                print(f\"   ❌ Failed to process\")\n", "                \n", "        except Exception as e:\n", "            print(f\"   ❌ Error: {e}\")\n", "    \n", "    # Summary statistics\n", "    if results:\n", "        print(f\"\\n📊 Summary of {len(results)} processed images:\")\n", "        print(\"=\" * 40)\n", "        \n", "        # Count predictions by class\n", "        class_counts = {}\n", "        for label in SATISFACTION_LABELS.values():\n", "            count = sum(1 for r in results if r['predicted_label'] == label)\n", "            class_counts[label] = count\n", "            print(f\"   {label}: {count} images ({count/len(results)*100:.1f}%)\")\n", "        \n", "        # Average confidence\n", "        avg_confidence = np.mean([r['confidence'] for r in results])\n", "        print(f\"\\n   Average Confidence: {avg_confidence:.3f}\")\n", "        \n", "        # Create summary visualization\n", "        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "        \n", "        # Class distribution\n", "        labels = list(class_counts.keys())\n", "        counts = list(class_counts.values())\n", "        colors = [COLORS[label] for label in labels]\n", "        \n", "        ax1.pie(counts, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)\n", "        ax1.set_title('Distribution of Predictions')\n", "        \n", "        # Confidence distribution\n", "        confidences = [r['confidence'] for r in results]\n", "        ax2.hist(confidences, bins=10, alpha=0.7, color='skyblue', edgecolor='black')\n", "        ax2.set_xlabel('Confidence Score')\n", "        ax2.set_ylabel('Number of Images')\n", "        ax2.set_title('Distribution of Confidence Scores')\n", "        ax2.axvline(avg_confidence, color='red', linestyle='--', label=f'Average: {avg_confidence:.3f}')\n", "        ax2.legend()\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        return results\n", "    else:\n", "        print(\"❌ No images were successfully processed.\")\n", "        return []\n", "\n", "# Example usage\n", "FOLDER_PATH = \"test_images/\"  # Change this to your image folder path\n", "\n", "if os.path.exists(FOLDER_PATH):\n", "    batch_results = test_image_folder(FOLDER_PATH, max_images=5, show_images=True)\nelse:\n", "    print(f\"❌ Folder not found: {FOLDER_PATH}\")\n", "    print(\"Please create a folder with test images or update FOLDER_PATH.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Webcam Testing (Optional)\n", "\n", "Test the model with real-time webcam input:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_webcam(duration_seconds=10, camera_id=0):\n", "    \"\"\"\n", "    Test model with webcam input\n", "    \n", "    Args:\n", "        duration_seconds: How long to run the test\n", "        camera_id: Camera device ID (usually 0)\n", "    \"\"\"\n", "    try:\n", "        import time\n", "        \n", "        print(f\"🎥 Starting webcam test for {duration_seconds} seconds...\")\n", "        print(\"Make sure your webcam is connected and working!\")\n", "        \n", "        cap = cv2.VideoCapture(camera_id)\n", "        \n", "        if not cap.isOpened():\n", "            print(f\"❌ Could not open camera {camera_id}\")\n", "            return\n", "        \n", "        start_time = time.time()\n", "        frame_count = 0\n", "        predictions = []\n", "        \n", "        while time.time() - start_time < duration_seconds:\n", "            ret, frame = cap.read()\n", "            if not ret:\n", "                print(\"❌ Failed to capture frame\")\n", "                break\n", "            \n", "            # Process every 10th frame to improve performance\n", "            if frame_count % 10 == 0:\n", "                # Convert BGR to RGB\n", "                rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)\n", "                \n", "                # Make prediction\n", "                result = predict_satisfaction(rgb_frame, verbose=False)\n", "                \n", "                if result:\n", "                    predictions.append(result)\n", "                    \n", "                    # Display current frame with prediction\n", "                    clear_output(wait=True)\n", "                    \n", "                    plt.figure(figsize=(10, 6))\n", "                    plt.imshow(rgb_frame)\n", "                    plt.title(f\"Live Prediction: {result['predicted_label']} \"\n", "                             f\"(Confidence: {result['confidence']:.3f})\\n\"\n", "                             f\"Time: {time.time() - start_time:.1f}s\")\n", "                    plt.axis('off')\n", "                    plt.show()\n", "            \n", "            frame_count += 1\n", "        \n", "        cap.release()\n", "        \n", "        # Show summary\n", "        if predictions:\n", "            print(f\"\\n📊 Webcam Test Summary ({len(predictions)} predictions):\")\n", "            \n", "            # Count predictions\n", "            class_counts = {}\n", "            for label in SATISFACTION_LABELS.values():\n", "                count = sum(1 for p in predictions if p['predicted_label'] == label)\n", "                class_counts[label] = count\n", "                print(f\"   {label}: {count} predictions\")\n", "            \n", "            # Most common prediction\n", "            most_common = max(class_counts, key=class_counts.get)\n", "            print(f\"\\n🎯 Most common prediction: {most_common}\")\n", "            \n", "            # Average confidence\n", "            avg_confidence = np.mean([p['confidence'] for p in predictions])\n", "            print(f\"📈 Average confidence: {avg_confidence:.3f}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error with webcam testing: {e}\")\n", "        print(\"Make sure you have a webcam connected and cv2 is properly installed.\")\n", "\n", "# Uncomment the line below to test with webcam\n", "# test_webcam(duration_seconds=5)\n", "\n", "print(\"🎥 Webcam testing function ready!\")\n", "print(\"Uncomment the line above to start webcam testing.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Model Performance Analysis\n", "\n", "Analyze the model's performance and characteristics:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_model_performance():\n", "    \"\"\"\n", "    Analyze and display model performance metrics\n", "    \"\"\"\n", "    if model is None:\n", "        print(\"❌ Model not loaded\")\n", "        return\n", "    \n", "    print(\"🔍 Model Performance Analysis\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Model architecture info\n", "    print(\"\\n📋 Model Information:\")\n", "    print(f\"   Input Shape: {model.input_shape}\")\n", "    print(f\"   Output Shape: {model.output_shape}\")\n", "    print(f\"   Total Parameters: {model.count_params():,}\")\n", "    print(f\"   Number of Layers: {len(model.layers)}\")\n", "    \n", "    # Class information\n", "    print(\"\\n🏷️ Class Labels:\")\n", "    for class_id, label in SATISFACTION_LABELS.items():\n", "        print(f\"   {class_id}: {label}\")\n", "    \n", "    # Expected performance (from training)\n", "    print(\"\\n📊 Expected Performance (from training):\")\n", "    print(\"   Test Accuracy: ~78.6%\")\n", "    print(\"   Model Type: CNN trained on FER2013 dataset\")\n", "    print(\"   Input: 48x48 grayscale facial images\")\n", "    \n", "    # Emotion mapping\n", "    print(\"\\n🎭 Original Emotion to Satisfaction Mapping:\")\n", "    emotion_mapping = {\n", "        'Satisfied': ['Happy', 'Surprise'],\n", "        'Neutral': ['Neutral'],\n", "        'Unsatisfied': ['<PERSON>', '<PERSON>s<PERSON><PERSON>', '<PERSON>', 'Sad']\n", "    }\n", "    \n", "    for satisfaction, emotions in emotion_mapping.items():\n", "        print(f\"   {satisfaction}: {', '.join(emotions)}\")\n", "    \n", "    # Usage recommendations\n", "    print(\"\\n💡 Usage Recommendations:\")\n", "    print(\"   • Best results with clear, front-facing facial images\")\n", "    print(\"   • Ensure good lighting and minimal occlusion\")\n", "    print(\"   • Model works on grayscale, so color is not important\")\n", "    print(\"   • Images are automatically resized to 48x48 pixels\")\n", "    print(\"   • Confidence scores above 0.7 are generally reliable\")\n", "\n", "# Run the analysis\n", "analyze_model_performance()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Create Sample Test Images\n", "\n", "Generate some sample test images if you don't have any:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_sample_test_images(num_images=3):\n", "    \"\"\"\n", "    Create sample test images for demonstration\n", "    (These are random images for testing the pipeline)\n", "    \"\"\"\n", "    print(f\"🎨 Creating {num_images} sample test images...\")\n", "    \n", "    # Create test_images directory if it doesn't exist\n", "    os.makedirs('test_images', exist_ok=True)\n", "    \n", "    sample_paths = []\n", "    \n", "    for i in range(num_images):\n", "        # Create a random image (in practice, use real face images)\n", "        # This is just for testing the pipeline\n", "        random_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)\n", "        \n", "        # Add some simple patterns to make it more interesting\n", "        cv2.circle(random_image, (50, 30), 10, (255, 255, 255), -1)  # Eyes\n", "        cv2.circle(random_image, (30, 30), 5, (0, 0, 0), -1)\n", "        cv2.circle(random_image, (70, 30), 5, (0, 0, 0), -1)\n", "        cv2.ellipse(random_image, (50, 70), (15, 8), 0, 0, 180, (0, 0, 0), 2)  # Mouth\n", "        \n", "        filename = f'test_images/sample_{i+1}.jpg'\n", "        cv2.imwrite(filename, random_image)\n", "        sample_paths.append(filename)\n", "        \n", "        print(f\"   ✅ Created: {filename}\")\n", "    \n", "    print(f\"\\n📁 Sample images saved in 'test_images/' folder\")\n", "    print(\"💡 Replace these with real facial images for better results!\")\n", "    \n", "    return sample_paths\n", "\n", "# Create sample images\n", "sample_image_paths = create_sample_test_images(3)\n", "\n", "# Test the sample images\n", "print(\"\\n🧪 Testing sample images:\")\n", "for path in sample_image_paths:\n", "    print(f\"\\n📸 Testing {path}:\")\n", "    result = predict_satisfaction(path)\n", "    \n", "    # Show the image\n", "    image = cv2.imread(path)\n", "    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)\n", "    \n", "    plt.figure(figsize=(6, 4))\n", "    plt.imshow(image)\n", "    plt.title(f\"Sample Image\\nPrediction: {result['predicted_label'] if result else 'Failed'}\")\n", "    plt.axis('off')\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. <PERSON><PERSON><PERSON> and <PERSON>shooting\n", "\n", "### Getting the Best Results:\n", "\n", "1. **Image Quality**: Use clear, well-lit images with visible faces\n", "2. **Face Detection**: The model works best with cropped face images\n", "3. **Lighting**: Ensure good lighting conditions\n", "4. **Angle**: Front-facing or slightly angled faces work best\n", "\n", "### Common Issues:\n", "\n", "- **Low Confidence**: May indicate unclear facial expressions or poor image quality\n", "- **Unexpected Results**: The model was trained on specific emotion categories\n", "- **File Not Found**: Make sure image paths are correct and files exist\n", "\n", "### Next Steps:\n", "\n", "1. Test with your own facial expression images\n", "2. Try different lighting conditions and angles\n", "3. Comp<PERSON> results with human perception\n", "4. Consider fine-tuning the model for your specific use case"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}