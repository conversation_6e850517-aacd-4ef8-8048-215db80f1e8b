#!/usr/bin/env python3
"""
Example usage of the Customer Satisfaction Model

This script demonstrates various ways to use the satisfaction classifier
for testing on real images.
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt
from test_satisfaction_model import SatisfactionClassifier

def example_single_image():
    """Example: Test a single image"""
    print("=== Testing Single Image ===")
    
    # Initialize classifier
    classifier = SatisfactionClassifier('satisfaction_model_best.h5')
    
    # Example with a sample image (you can replace with your own image path)
    image_path = "sample_face.jpg"  # Replace with actual image path
    
    try:
        # Get prediction
        result = classifier.predict_single(image_path, return_probabilities=True)
        
        if result:
            print(f"Image: {image_path}")
            print(f"Prediction: {result['predicted_label']}")
            print(f"Confidence: {result['confidence']:.3f}")
            print("All probabilities:")
            for label, prob in result['probabilities'].items():
                print(f"  {label}: {prob:.3f}")
            
            # Visualize the result
            classifier.visualize_prediction(image_path)
        else:
            print(f"Could not process image: {image_path}")
            
    except FileNotFoundError:
        print(f"Image file not found: {image_path}")
        print("Please provide a valid image path")

def example_create_test_image():
    """Example: Create a simple test image and predict"""
    print("\n=== Creating Test Image ===")
    
    # Create a simple test image (random noise for demonstration)
    # In practice, you would use real face images
    test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    
    # Save the test image
    cv2.imwrite("test_image.jpg", test_image)
    print("Created test_image.jpg")
    
    # Initialize classifier
    classifier = SatisfactionClassifier('satisfaction_model_best.h5')
    
    # Test the created image
    result = classifier.predict_single("test_image.jpg", return_probabilities=True)
    
    if result:
        print(f"Prediction: {result['predicted_label']}")
        print(f"Confidence: {result['confidence']:.3f}")

def example_batch_testing():
    """Example: Test multiple images"""
    print("\n=== Batch Testing ===")
    
    # Create some sample images for testing
    sample_images = []
    for i in range(3):
        # Create random test images
        test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        filename = f"sample_{i}.jpg"
        cv2.imwrite(filename, test_image)
        sample_images.append(filename)
    
    print(f"Created {len(sample_images)} sample images")
    
    # Initialize classifier
    classifier = SatisfactionClassifier('satisfaction_model_best.h5')
    
    # Test all images
    results = classifier.predict_batch(sample_images)
    
    # Print summary
    print(f"\nProcessed {len(results)} images:")
    for result in results:
        print(f"  {result['image_path']}: {result['predicted_label']} "
              f"({result['confidence']:.3f})")

def example_webcam_instructions():
    """Example: Instructions for webcam testing"""
    print("\n=== Webcam Testing Instructions ===")
    print("To test with your webcam, run:")
    print("python test_satisfaction_model.py --camera")
    print("\nControls:")
    print("- Press 'q' to quit")
    print("- Press 's' to save current frame")
    print("\nMake sure you have a webcam connected!")

def example_command_line_usage():
    """Example: Show command line usage"""
    print("\n=== Command Line Usage Examples ===")
    print("1. Test a single image:")
    print("   python test_satisfaction_model.py --image path/to/your/image.jpg --visualize")
    
    print("\n2. Test all images in a folder:")
    print("   python test_satisfaction_model.py --folder path/to/image/folder/")
    
    print("\n3. Test with webcam:")
    print("   python test_satisfaction_model.py --camera")
    
    print("\n4. Test with custom model:")
    print("   python test_satisfaction_model.py --model custom_model.h5 --image test.jpg")

def example_programmatic_usage():
    """Example: Using the classifier programmatically"""
    print("\n=== Programmatic Usage ===")
    
    try:
        # Initialize classifier
        classifier = SatisfactionClassifier('satisfaction_model_best.h5')
        
        # Example 1: Test with numpy array
        print("Testing with numpy array...")
        random_image = np.random.randint(0, 255, (48, 48), dtype=np.uint8)
        result = classifier.predict_single(random_image)
        if result:
            print(f"Random image prediction: {result['predicted_label']} "
                  f"({result['confidence']:.3f})")
        
        # Example 2: Get detailed probabilities
        print("\nGetting detailed probabilities...")
        result_detailed = classifier.predict_single(random_image, return_probabilities=True)
        if result_detailed:
            print("Detailed probabilities:")
            for label, prob in result_detailed['probabilities'].items():
                print(f"  {label}: {prob:.4f}")
        
    except Exception as e:
        print(f"Error in programmatic usage: {e}")
        print("Make sure the model file 'satisfaction_model_best.h5' exists")

def main():
    """Run all examples"""
    print("Customer Satisfaction Model - Usage Examples")
    print("=" * 50)
    
    # Show command line usage
    example_command_line_usage()
    
    # Show webcam instructions
    example_webcam_instructions()
    
    # Try programmatic usage
    example_programmatic_usage()
    
    # Create and test sample images
    example_create_test_image()
    example_batch_testing()
    
    print("\n" + "=" * 50)
    print("Examples completed!")
    print("\nTo test with real face images:")
    print("1. Get some face images (jpg, png, etc.)")
    print("2. Run: python test_satisfaction_model.py --image your_image.jpg --visualize")
    print("3. Or test a folder: python test_satisfaction_model.py --folder your_image_folder/")

if __name__ == "__main__":
    main()
