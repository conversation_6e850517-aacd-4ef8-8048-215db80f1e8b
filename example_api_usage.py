#!/usr/bin/env python3
"""
Example script demonstrating how to use the real-time satisfaction detector with the Session Summaries API.

This script shows different ways to run the detector with API integration.
"""

import subprocess
import sys
import time

def run_detector_with_api():
    """Example: Run detector with API integration"""
    print("🚀 Running satisfaction detector with API integration...")
    print("📡 Make sure your API server is running on http://localhost")
    print("⏱️  This will run for 30 seconds then automatically quit\n")
    
    # Example command with API integration
    cmd = [
        sys.executable, "realtime_satisfaction_detector.py",
        "--api-url", "http://localhost",
        "--session-id", f"demo_session_{int(time.time())}",
        "--camera", "0",
        "--min-face-size", "80"
    ]
    
    try:
        # Run the detector
        process = subprocess.Popen(cmd)
        
        # Let it run for 30 seconds
        time.sleep(30)
        
        # Terminate the process
        process.terminate()
        process.wait()
        
        print("\n✅ Demo completed! Check your API for the session data.")
        
    except KeyboardInterrupt:
        print("\n⏹️ Demo interrupted by user")
        if process:
            process.terminate()
    except Exception as e:
        print(f"❌ Error running demo: {e}")

def run_detector_local_only():
    """Example: Run detector without API (local only)"""
    print("🚀 Running satisfaction detector in local mode...")
    print("📊 Session data will only be displayed locally\n")
    
    cmd = [
        sys.executable, "realtime_satisfaction_detector.py",
        "--camera", "0"
    ]
    
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n⏹️ Detector stopped by user")

def test_cameras():
    """Test available cameras"""
    print("🔍 Testing available cameras...\n")
    
    cmd = [
        sys.executable, "realtime_satisfaction_detector.py",
        "--test-cameras"
    ]
    
    subprocess.run(cmd)

def show_help():
    """Show help for the detector"""
    print("📋 Showing detector help...\n")
    
    cmd = [
        sys.executable, "realtime_satisfaction_detector.py",
        "--help"
    ]
    
    subprocess.run(cmd)

def main():
    print("="*60)
    print("🎯 Real-time Satisfaction Detector - API Integration Demo")
    print("="*60)
    print()
    print("Choose an option:")
    print("1. Run with API integration (demo)")
    print("2. Run in local mode only")
    print("3. Test available cameras")
    print("4. Show detector help")
    print("5. Exit")
    print()
    
    while True:
        try:
            choice = input("Enter your choice (1-5): ").strip()
            
            if choice == "1":
                run_detector_with_api()
                break
            elif choice == "2":
                run_detector_local_only()
                break
            elif choice == "3":
                test_cameras()
                break
            elif choice == "4":
                show_help()
                break
            elif choice == "5":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please enter 1-5.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
