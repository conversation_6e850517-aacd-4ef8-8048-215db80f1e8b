#!/usr/bin/env python3
"""
Script pour afficher l'architecture complète du modèle de satisfaction client
"""

import os
import tensorflow as tf
from tensorflow.keras.models import load_model
import numpy as np

def display_model_architecture():
    """Affiche l'architecture complète du modèle de satisfaction client"""
    
    model_path = 'satisfaction_model_best.h5'
    
    if not os.path.exists(model_path):
        print(f"❌ Modèle non trouvé: {model_path}")
        print("Veuillez d'abord entraîner le modèle avec customer_satisfaction_classifier.ipynb")
        return
    
    print("🔍 ARCHITECTURE DU MODÈLE DE SATISFACTION CLIENT")
    print("=" * 60)
    
    try:
        # Charger le modèle
        model = load_model(model_path)
        
        # Informations générales
        print("\n📋 INFORMATIONS GÉNÉRALES:")
        print(f"   • Forme d'entrée: {model.input_shape}")
        print(f"   • Forme de sortie: {model.output_shape}")
        print(f"   • Nombre total de paramètres: {model.count_params():,}")
        print(f"   • Nombre de couches: {len(model.layers)}")
        
        # Classes de sortie
        print("\n🏷️ CLASSES DE SORTIE:")
        satisfaction_labels = {
            0: 'Satisfied (Satisfait)',
            1: 'Neutral (Neutre)', 
            2: 'Unsatisfied (Insatisfait)'
        }
        for class_id, label in satisfaction_labels.items():
            print(f"   • Classe {class_id}: {label}")
        
        # Architecture détaillée
        print("\n🏗️ ARCHITECTURE DÉTAILLÉE:")
        print("-" * 60)
        
        # Résumé du modèle
        model.summary()
        
        print("\n📊 DÉTAILS DES COUCHES:")
        print("-" * 60)
        
        for i, layer in enumerate(model.layers):
            print(f"\nCouche {i+1}: {layer.name}")
            print(f"   Type: {type(layer).__name__}")
            print(f"   Forme de sortie: {layer.output_shape}")
            
            # Paramètres spécifiques selon le type de couche
            if hasattr(layer, 'filters'):
                print(f"   Filtres: {layer.filters}")
            if hasattr(layer, 'kernel_size'):
                print(f"   Taille du noyau: {layer.kernel_size}")
            if hasattr(layer, 'strides'):
                print(f"   Pas (stride): {layer.strides}")
            if hasattr(layer, 'padding'):
                print(f"   Padding: {layer.padding}")
            if hasattr(layer, 'activation'):
                if hasattr(layer.activation, '__name__'):
                    print(f"   Activation: {layer.activation.__name__}")
            if hasattr(layer, 'pool_size'):
                print(f"   Taille du pool: {layer.pool_size}")
            if hasattr(layer, 'rate'):
                print(f"   Taux de dropout: {layer.rate}")
            if hasattr(layer, 'units'):
                print(f"   Unités: {layer.units}")
            
            # Nombre de paramètres
            params = layer.count_params()
            if params > 0:
                print(f"   Paramètres: {params:,}")
        
        # Informations sur l'entraînement
        print("\n🎯 INFORMATIONS D'ENTRAÎNEMENT:")
        print("-" * 60)
        print("   • Dataset: FER2013 (Facial Expression Recognition)")
        print("   • Taille d'image: 48x48 pixels en niveaux de gris")
        print("   • Augmentation de données: Rotation, zoom, décalage")
        print("   • Optimiseur: Adam (learning_rate=0.001)")
        print("   • Fonction de perte: categorical_crossentropy")
        print("   • Métrique: accuracy")
        print("   • Callbacks: ModelCheckpoint, EarlyStopping, ReduceLROnPlateau")
        print("   • Précision attendue: ~78.6%")
        
        # Mapping des émotions
        print("\n🎭 MAPPING ÉMOTIONS → SATISFACTION:")
        print("-" * 60)
        emotion_mapping = {
            'Satisfied': ['Happy', 'Surprise'],
            'Neutral': ['Neutral'],
            'Unsatisfied': ['Angry', 'Disgust', 'Fear', 'Sad']
        }
        
        for satisfaction, emotions in emotion_mapping.items():
            print(f"   • {satisfaction}: {', '.join(emotions)}")
        
        # Architecture en résumé
        print("\n📐 RÉSUMÉ DE L'ARCHITECTURE:")
        print("-" * 60)
        print("   1. BLOC CONVOLUTIONNEL 1:")
        print("      • Conv2D(32, 3x3) + BatchNorm + ReLU")
        print("      • Conv2D(32, 3x3) + BatchNorm + ReLU")
        print("      • MaxPooling2D(2x2)")
        print("      • Dropout(0.25)")
        print()
        print("   2. BLOC CONVOLUTIONNEL 2:")
        print("      • Conv2D(64, 3x3) + BatchNorm + ReLU")
        print("      • Conv2D(64, 3x3) + BatchNorm + ReLU")
        print("      • MaxPooling2D(2x2)")
        print("      • Dropout(0.25)")
        print()
        print("   3. BLOC CONVOLUTIONNEL 3:")
        print("      • Conv2D(128, 3x3) + BatchNorm + ReLU")
        print("      • Conv2D(128, 3x3) + BatchNorm + ReLU")
        print("      • MaxPooling2D(2x2)")
        print("      • Dropout(0.25)")
        print()
        print("   4. COUCHES DENSES:")
        print("      • Flatten()")
        print("      • Dense(512) + BatchNorm + ReLU + Dropout(0.5)")
        print("      • Dense(256) + BatchNorm + ReLU + Dropout(0.5)")
        print("      • Dense(3, softmax) - Sortie finale")
        
        print("\n✅ Architecture affichée avec succès!")
        
    except Exception as e:
        print(f"❌ Erreur lors du chargement du modèle: {e}")
        print("Vérifiez que le modèle a été correctement entraîné.")

if __name__ == "__main__":
    display_model_architecture()
