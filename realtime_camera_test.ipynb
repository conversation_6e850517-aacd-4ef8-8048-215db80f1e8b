{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Real-Time Customer Satisfaction Testing with Camera\n", "\n", "This notebook provides real-time testing of your customer satisfaction model using OpenCV camera feed.\n", "\n", "## Features:\n", "- Live camera feed with real-time predictions\n", "- Face detection for better accuracy\n", "- Confidence scores and prediction history\n", "- Recording and saving capabilities\n", "- Performance optimization for smooth video"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import cv2\n", "import numpy as np\n", "import tensorflow as tf\n", "from tensorflow.keras.models import load_model\n", "import matplotlib.pyplot as plt\n", "import time\n", "import os\n", "from collections import deque\n", "import threading\n", "from IPython.display import display, clear_output, HTML\n", "import ipywidgets as widgets\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"✅ Libraries imported successfully!\")\n", "print(f\"OpenCV version: {cv2.__version__}\")\n", "print(f\"TensorFlow version: {tf.__version__}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Load Model and Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration\n", "MODEL_PATH = 'satisfaction_model_best.h5'\n", "IMAGE_SIZE = (48, 48)\n", "\n", "# Class labels and colors\n", "SATISFACTION_LABELS = {\n", "    0: 'Satisfied',\n", "    1: 'Neutral', \n", "    2: 'Unsatisfied'\n", "}\n", "\n", "# Colors for OpenCV (BGR format)\n", "COLORS_BGR = {\n", "    'Satisfied': (0, 255, 0),      # Green\n", "    'Neutral': (0, 255, 255),      # Yellow\n", "    'Unsatisfied': (0, 0, 255)     # Red\n", "}\n", "\n", "# Load the model\n", "try:\n", "    if os.path.exists(MODEL_PATH):\n", "        print(f\"🔄 Loading model from {MODEL_PATH}...\")\n", "        model = load_model(MODEL_PATH)\n", "        print(\"✅ Model loaded successfully!\")\n", "        print(f\"📊 Model input shape: {model.input_shape}\")\n", "        print(f\"📊 Model output shape: {model.output_shape}\")\n", "    else:\n", "        print(f\"❌ Model file not found: {MODEL_PATH}\")\n", "        model = None\n", "except Exception as e:\n", "    print(f\"❌ Error loading model: {e}\")\n", "    model = None\n", "\n", "# Load face detection cascade\n", "try:\n", "    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')\n", "    print(\"✅ Face detection cascade loaded!\")\n", "except Exception as e:\n", "    print(f\"⚠️ Could not load face cascade: {e}\")\n", "    face_cascade = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Preprocessing and Prediction Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def preprocess_face_for_model(face_roi):\n", "    \"\"\"\n", "    Preprocess face region for model prediction\n", "    \n", "    Args:\n", "        face_roi: Face region of interest (numpy array)\n", "        \n", "    Returns:\n", "        Preprocessed image ready for model\n", "    \"\"\"\n", "    # Convert to grayscale if needed\n", "    if len(face_roi.shape) == 3:\n", "        face_gray = cv2.cvtColor(face_roi, cv2.COLOR_BGR2GRAY)\n", "    else:\n", "        face_gray = face_roi.copy()\n", "    \n", "    # Resize to model input size\n", "    face_resized = cv2.resize(face_gray, IMAGE_SIZE)\n", "    \n", "    # Normalize\n", "    face_normalized = face_resized.astype(np.float32) / 255.0\n", "    \n", "    # Add batch and channel dimensions\n", "    face_input = np.expand_dims(face_normalized, axis=0)\n", "    face_input = np.expand_dims(face_input, axis=-1)\n", "    \n", "    return face_input\n", "\n", "def predict_satisfaction_realtime(face_roi):\n", "    \"\"\"\n", "    Predict satisfaction from face region in real-time\n", "    \n", "    Args:\n", "        face_roi: Face region of interest\n", "        \n", "    Returns:\n", "        Dictionary with prediction results\n", "    \"\"\"\n", "    if model is None:\n", "        return None\n", "    \n", "    try:\n", "        # Preprocess\n", "        processed_face = preprocess_face_for_model(face_roi)\n", "        \n", "        # Predict\n", "        predictions = model.predict(processed_face, verbose=0)\n", "        \n", "        # Get results\n", "        predicted_class = np.argmax(predictions[0])\n", "        confidence = predictions[0][predicted_class]\n", "        predicted_label = SATISFACTION_LABELS[predicted_class]\n", "        \n", "        return {\n", "            'class': predicted_class,\n", "            'label': predicted_label,\n", "            'confidence': float(confidence),\n", "            'probabilities': predictions[0].tolist(),\n", "            'color': COLORS_BGR[predicted_label]\n", "        }\n", "        \n", "    except Exception as e:\n", "        print(f\"Prediction error: {e}\")\n", "        return None\n", "\n", "def detect_faces(frame):\n", "    \"\"\"\n", "    Detect faces in frame using Haar cascade\n", "    \n", "    Args:\n", "        frame: Input frame\n", "        \n", "    Returns:\n", "        List of face rectangles (x, y, w, h)\n", "    \"\"\"\n", "    if face_cascade is None:\n", "        return []\n", "    \n", "    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)\n", "    faces = face_cascade.detectMultiScale(\n", "        gray,\n", "        scaleFactor=1.1,\n", "        minNeighbors=5,\n", "        minSize=(30, 30)\n", "    )\n", "    \n", "    return faces\n", "\n", "print(\"✅ Preprocessing and prediction functions ready!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Real-Time Camera Testing"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class RealTimeSatisfactionTester:\n", "    def __init__(self, camera_id=0):\n", "        self.camera_id = camera_id\n", "        self.cap = None\n", "        self.is_running = False\n", "        self.prediction_history = deque(maxlen=30)  # Keep last 30 predictions\n", "        self.frame_count = 0\n", "        self.fps = 0\n", "        self.last_time = time.time()\n", "        \n", "    def start_camera(self):\n", "        \"\"\"Initialize camera\"\"\"\n", "        try:\n", "            self.cap = cv2.VideoCapture(self.camera_id)\n", "            if not self.cap.isOpened():\n", "                print(f\"❌ Could not open camera {self.camera_id}\")\n", "                return False\n", "            \n", "            # Set camera properties for better performance\n", "            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)\n", "            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)\n", "            self.cap.set(cv2.CAP_PROP_FPS, 30)\n", "            \n", "            print(\"✅ Camera initialized successfully!\")\n", "            return True\n", "            \n", "        except Exception as e:\n", "            print(f\"❌ Error initializing camera: {e}\")\n", "            return False\n", "    \n", "    def stop_camera(self):\n", "        \"\"\"Release camera resources\"\"\"\n", "        self.is_running = False\n", "        if self.cap:\n", "            self.cap.release()\n", "        cv2.destroyAllWindows()\n", "        print(\"📹 Camera stopped and resources released\")\n", "    \n", "    def calculate_fps(self):\n", "        \"\"\"Calculate current FPS\"\"\"\n", "        current_time = time.time()\n", "        if current_time - self.last_time >= 1.0:\n", "            self.fps = self.frame_count\n", "            self.frame_count = 0\n", "            self.last_time = current_time\n", "        self.frame_count += 1\n", "    \n", "    def draw_prediction_info(self, frame, prediction, face_rect):\n", "        \"\"\"Draw prediction information on frame\"\"\"\n", "        if prediction is None:\n", "            return frame\n", "        \n", "        x, y, w, h = face_rect\n", "        label = prediction['label']\n", "        confidence = prediction['confidence']\n", "        color = prediction['color']\n", "        \n", "        # Draw face rectangle\n", "        cv2.rectangle(frame, (x, y), (x + w, y + h), color, 2)\n", "        \n", "        # Draw label and confidence\n", "        text = f\"{label}: {confidence:.2f}\"\n", "        text_size = cv2.getTextSize(text, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]\n", "        \n", "        # Background rectangle for text\n", "        cv2.rectangle(frame, (x, y - text_size[1] - 10), \n", "                     (x + text_size[0], y), color, -1)\n", "        \n", "        # Text\n", "        cv2.putText(frame, text, (x, y - 5), \n", "                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)\n", "        \n", "        return frame\n", "    \n", "    def draw_statistics(self, frame):\n", "        \"\"\"Draw statistics on frame\"\"\"\n", "        height, width = frame.shape[:2]\n", "        \n", "        # FPS\n", "        cv2.putText(frame, f\"FPS: {self.fps}\", (10, 30), \n", "                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)\n", "        \n", "        # Prediction history summary\n", "        if self.prediction_history:\n", "            # Count recent predictions\n", "            recent_predictions = list(self.prediction_history)[-10:]  # Last 10\n", "            if recent_predictions:\n", "                labels = [p['label'] for p in recent_predictions]\n", "                most_common = max(set(labels), key=labels.count)\n", "                avg_confidence = np.mean([p['confidence'] for p in recent_predictions])\n", "                \n", "                cv2.putText(frame, f\"Recent: {most_common} ({avg_confidence:.2f})\", \n", "                           (10, height - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, \n", "                           (255, 255, 255), 2)\n", "        \n", "        # Instructions\n", "        cv2.putText(frame, \"Press 'q' to quit, 's' to save frame\", \n", "                   (10, height - 50), cv2.FONT_HERSHEY_SIMPLEX, 0.5, \n", "                   (200, 200, 200), 1)\n", "        \n", "        return frame\n", "    \n", "    def run_realtime_test(self, duration_seconds=None):\n", "        \"\"\"Run real-time satisfaction testing\"\"\"\n", "        if not self.start_camera():\n", "            return\n", "        \n", "        print(\"🎥 Starting real-time satisfaction testing...\")\n", "        print(\"Controls:\")\n", "        print(\"  - Press 'q' to quit\")\n", "        print(\"  - Press 's' to save current frame\")\n", "        print(\"  - Press 'r' to reset prediction history\")\n", "        \n", "        self.is_running = True\n", "        start_time = time.time()\n", "        saved_frames = 0\n", "        \n", "        try:\n", "            while self.is_running:\n", "                ret, frame = self.cap.read()\n", "                if not ret:\n", "                    print(\"❌ Failed to capture frame\")\n", "                    break\n", "                \n", "                # Calculate FPS\n", "                self.calculate_fps()\n", "                \n", "                # Detect faces\n", "                faces = detect_faces(frame)\n", "                \n", "                # Process each face\n", "                for (x, y, w, h) in faces:\n", "                    # Extract face region\n", "                    face_roi = frame[y:y+h, x:x+w]\n", "                    \n", "                    # Predict satisfaction\n", "                    prediction = predict_satisfaction_realtime(face_roi)\n", "                    \n", "                    if prediction:\n", "                        # Add to history\n", "                        prediction['timestamp'] = time.time()\n", "                        self.prediction_history.append(prediction)\n", "                        \n", "                        # Draw prediction info\n", "                        frame = self.draw_prediction_info(frame, prediction, (x, y, w, h))\n", "                \n", "                # Draw statistics\n", "                frame = self.draw_statistics(frame)\n", "                \n", "                # Show frame\n", "                cv2.imshow('Customer Satisfaction - Real-Time Testing', frame)\n", "                \n", "                # Handle key presses\n", "                key = cv2.wait<PERSON><PERSON>(1) & 0xFF\n", "                \n", "                if key == ord('q'):\n", "                    break\n", "                elif key == ord('s'):\n", "                    # Save current frame\n", "                    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "                    filename = f\"satisfaction_frame_{timestamp}.jpg\"\n", "                    cv2.imwrite(filename, frame)\n", "                    saved_frames += 1\n", "                    print(f\"💾 Frame saved: {filename}\")\n", "                elif key == ord('r'):\n", "                    # Reset prediction history\n", "                    self.prediction_history.clear()\n", "                    print(\"🔄 Prediction history reset\")\n", "                \n", "                # Check duration limit\n", "                if duration_seconds and (time.time() - start_time) > duration_seconds:\n", "                    print(f\"⏰ Duration limit reached ({duration_seconds}s)\")\n", "                    break\n", "                    \n", "        except KeyboardInterrupt:\n", "            print(\"\\n⏹️ Testing interrupted by user\")\n", "        \n", "        finally:\n", "            self.stop_camera()\n", "            \n", "            # Show summary\n", "            self.show_session_summary(saved_frames)\n", "    \n", "    def show_session_summary(self, saved_frames):\n", "        \"\"\"Show summary of the testing session\"\"\"\n", "        print(\"\\n\" + \"=\"*50)\n", "        print(\"📊 SESSION SUMMARY\")\n", "        print(\"=\"*50)\n", "        \n", "        if self.prediction_history:\n", "            predictions = list(self.prediction_history)\n", "            \n", "            # Count predictions by class\n", "            class_counts = {}\n", "            for label in SATISFACTION_LABELS.values():\n", "                count = sum(1 for p in predictions if p['label'] == label)\n", "                class_counts[label] = count\n", "            \n", "            print(f\"📈 Total predictions: {len(predictions)}\")\n", "            print(f\"💾 Frames saved: {saved_frames}\")\n", "            print(\"\\n🏷️ Prediction distribution:\")\n", "            \n", "            for label, count in class_counts.items():\n", "                percentage = (count / len(predictions)) * 100 if predictions else 0\n", "                print(f\"   {label}: {count} ({percentage:.1f}%)\")\n", "            \n", "            # Average confidence\n", "            avg_confidence = np.mean([p['confidence'] for p in predictions])\n", "            print(f\"\\n📊 Average confidence: {avg_confidence:.3f}\")\n", "            \n", "            # Most common prediction\n", "            if class_counts:\n", "                most_common = max(class_counts, key=class_counts.get)\n", "                print(f\"🎯 Most common: {most_common}\")\n", "        else:\n", "            print(\"❌ No predictions recorded\")\n", "\n", "print(\"✅ Real-time tester class ready!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Start Real-Time Testing\n", "\n", "Run this cell to start the real-time camera testing:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create the real-time tester\n", "tester = RealTimeSatisfactionTester(camera_id=0)  # Change camera_id if needed\n", "\n", "# Start real-time testing\n", "print(\"🚀 Starting real-time satisfaction testing...\")\n", "print(\"\\n📋 Instructions:\")\n", "print(\"   • Make sure your camera is connected\")\n", "print(\"   • Position your face in front of the camera\")\n", "print(\"   • The model will detect faces and predict satisfaction in real-time\")\n", "print(\"   • Press 'q' to quit, 's' to save frame, 'r' to reset history\")\n", "print(\"\\n⚡ Starting in 3 seconds...\")\n", "\n", "import time\n", "time.sleep(3)\n", "\n", "# Run the test (remove duration_seconds for unlimited testing)\n", "tester.run_realtime_test(duration_seconds=None)  # Set to None for unlimited time"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Quick Test (Limited Duration)\n", "\n", "Run this cell for a quick 30-second test:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Quick 30-second test\n", "print(\"⚡ Quick 30-second test starting...\")\n", "\n", "quick_tester = RealTimeSatisfactionTester(camera_id=0)\n", "quick_tester.run_realtime_test(duration_seconds=30)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Camera Settings and Troubleshooting"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def test_camera_availability():\n", "    \"\"\"Test which cameras are available\"\"\"\n", "    print(\"🔍 Testing camera availability...\")\n", "    \n", "    available_cameras = []\n", "    \n", "    for i in range(5):  # Test first 5 camera indices\n", "        cap = cv2.VideoCapture(i)\n", "        if cap.isOpened():\n", "            ret, frame = cap.read()\n", "            if ret:\n", "                height, width = frame.shape[:2]\n", "                available_cameras.append({\n", "                    'id': i,\n", "                    'resolution': f\"{width}x{height}\"\n", "                })\n", "                print(f\"✅ Camera {i}: Available ({width}x{height})\")\n", "            cap.release()\n", "        else:\n", "            print(f\"❌ Camera {i}: Not available\")\n", "    \n", "    if available_cameras:\n", "        print(f\"\\n📹 Found {len(available_cameras)} available camera(s)\")\n", "        return available_cameras\n", "    else:\n", "        print(\"\\n❌ No cameras found!\")\n", "        print(\"Troubleshooting tips:\")\n", "        print(\"   • Make sure your camera is connected\")\n", "        print(\"   • Check if other applications are using the camera\")\n", "        print(\"   • Try restarting your computer\")\n", "        print(\"   • Check camera permissions\")\n", "        return []\n", "\n", "def show_camera_info():\n", "    \"\"\"Show detailed camera information\"\"\"\n", "    cap = cv2.VideoCapture(0)\n", "    \n", "    if cap.isOpened():\n", "        print(\"📹 Camera Information:\")\n", "        print(f\"   Width: {int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))}\")\n", "        print(f\"   Height: {int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))}\")\n", "        print(f\"   FPS: {int(cap.get(cv2.CAP_PROP_FPS))}\")\n", "        print(f\"   Backend: {cap.getBackendName()}\")\n", "        cap.release()\n", "    else:\n", "        print(\"❌ Could not access camera\")\n", "\n", "# Test camera availability\n", "available_cameras = test_camera_availability()\n", "print(\"\\n\" + \"=\"*40)\n", "show_camera_info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Performance Optimization Settings"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class OptimizedRealTimeTester(RealTimeSatisfactionTester):\n", "    \"\"\"Optimized version for better performance\"\"\"\n", "    \n", "    def __init__(self, camera_id=0, skip_frames=3, min_face_size=80):\n", "        super().__init__(camera_id)\n", "        self.skip_frames = skip_frames  # Process every Nth frame\n", "        self.min_face_size = min_face_size\n", "        self.frame_skip_counter = 0\n", "        self.last_prediction = None\n", "    \n", "    def run_optimized_test(self, duration_seconds=None):\n", "        \"\"\"Run optimized real-time testing\"\"\"\n", "        if not self.start_camera():\n", "            return\n", "        \n", "        print(\"⚡ Starting OPTIMIZED real-time testing...\")\n", "        print(f\"🔧 Performance settings:\")\n", "        print(f\"   • Processing every {self.skip_frames} frames\")\n", "        print(f\"   • Minimum face size: {self.min_face_size}px\")\n", "        print(\"\\nControls: 'q' to quit, 's' to save, 'r' to reset\")\n", "        \n", "        self.is_running = True\n", "        start_time = time.time()\n", "        saved_frames = 0\n", "        \n", "        try:\n", "            while self.is_running:\n", "                ret, frame = self.cap.read()\n", "                if not ret:\n", "                    break\n", "                \n", "                self.calculate_fps()\n", "                \n", "                # Only process every Nth frame for prediction\n", "                if self.frame_skip_counter % self.skip_frames == 0:\n", "                    faces = detect_faces(frame)\n", "                    \n", "                    # Filter faces by minimum size\n", "                    large_faces = [(x, y, w, h) for (x, y, w, h) in faces \n", "                                 if w >= self.min_face_size and h >= self.min_face_size]\n", "                    \n", "                    if large_faces:\n", "                        # Process only the largest face for better performance\n", "                        largest_face = max(large_faces, key=lambda f: f[2] * f[3])\n", "                        x, y, w, h = largest_face\n", "                        \n", "                        face_roi = frame[y:y+h, x:x+w]\n", "                        prediction = predict_satisfaction_realtime(face_roi)\n", "                        \n", "                        if prediction:\n", "                            self.last_prediction = prediction\n", "                            prediction['timestamp'] = time.time()\n", "                            self.prediction_history.append(prediction)\n", "                \n", "                # Always draw the last prediction (for smooth display)\n", "                if self.last_prediction:\n", "                    faces = detect_faces(frame)\n", "                    if faces:\n", "                        largest_face = max(faces, key=lambda f: f[2] * f[3])\n", "                        frame = self.draw_prediction_info(frame, self.last_prediction, largest_face)\n", "                \n", "                frame = self.draw_statistics(frame)\n", "                \n", "                # Add optimization info\n", "                cv2.putText(frame, f\"Optimized (skip: {self.skip_frames})\", \n", "                           (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 255), 1)\n", "                \n", "                cv2.imshow('Optimized Satisfaction Testing', frame)\n", "                \n", "                # Handle keys\n", "                key = cv2.wait<PERSON><PERSON>(1) & 0xFF\n", "                if key == ord('q'):\n", "                    break\n", "                elif key == ord('s'):\n", "                    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "                    filename = f\"optimized_frame_{timestamp}.jpg\"\n", "                    cv2.imwrite(filename, frame)\n", "                    saved_frames += 1\n", "                    print(f\"💾 Frame saved: {filename}\")\n", "                elif key == ord('r'):\n", "                    self.prediction_history.clear()\n", "                    self.last_prediction = None\n", "                    print(\"🔄 Reset complete\")\n", "                \n", "                self.frame_skip_counter += 1\n", "                \n", "                if duration_seconds and (time.time() - start_time) > duration_seconds:\n", "                    break\n", "                    \n", "        except KeyboardInterrupt:\n", "            print(\"\\n⏹️ Testing stopped\")\n", "        finally:\n", "            self.stop_camera()\n", "            self.show_session_summary(saved_frames)\n", "\n", "print(\"✅ Optimized tester ready!\")\n", "print(\"💡 Use OptimizedRealTimeTester for better performance on slower computers\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Run Optimized Testing\n", "\n", "Use this for better performance on slower computers:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run optimized testing\n", "print(\"⚡ Starting optimized real-time testing...\")\n", "\n", "# Create optimized tester\n", "# skip_frames=3 means process every 3rd frame (faster)\n", "# min_face_size=80 means ignore small faces (more accurate)\n", "optimized_tester = OptimizedRealTimeTester(\n", "    camera_id=0, \n", "    skip_frames=3, \n", "    min_face_size=80\n", ")\n", "\n", "# Run the optimized test\n", "optimized_tester.run_optimized_test(duration_seconds=None)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. <PERSON><PERSON><PERSON> for Best Results\n", "\n", "### 🎯 **For Accurate Predictions:**\n", "1. **Good Lighting**: Ensure your face is well-lit\n", "2. **Clear View**: Face the camera directly\n", "3. **Stable Position**: Keep your head relatively still\n", "4. **Appropriate Distance**: Stay 2-4 feet from camera\n", "5. **Clean Background**: Avoid cluttered backgrounds\n", "\n", "### ⚡ **For Better Performance:**\n", "1. **Close Other Apps**: Free up system resources\n", "2. **Use Optimized Version**: For slower computers\n", "3. **Lower Resolution**: If experiencing lag\n", "4. **Good Hardware**: Better CPU/GPU helps\n", "\n", "### 🔧 **Troubleshooting:**\n", "- **No Camera**: Check camera permissions and connections\n", "- **Low FPS**: Use optimized version or close other apps\n", "- **No Face Detection**: Improve lighting and face positioning\n", "- **Inaccurate Predictions**: Ensure clear facial expressions\n", "\n", "### 📊 **Understanding Results:**\n", "- **Satisfied**: Happy, surprised expressions\n", "- **Neutral**: Calm, relaxed expressions  \n", "- **Unsatisfied**: Angry, sad, fearful expressions\n", "- **Confidence > 0.7**: Generally reliable predictions\n", "- **Confidence < 0.5**: May indicate unclear expressions"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}