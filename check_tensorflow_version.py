import tensorflow as tf
import numpy as np
import cv2
import sys

print("="*50)
print("📊 Version Information")
print("="*50)
print(f"Python version: {sys.version}")
print(f"TensorFlow version: {tf.__version__}")
print(f"NumPy version: {np.__version__}")
print(f"OpenCV version: {cv2.__version__}")
print("="*50)

# Check if GPU is available
print("\n🔍 GPU Information")
print("="*50)
if tf.config.list_physical_devices('GPU'):
    print("✅ GPU is available")
    for gpu in tf.config.list_physical_devices('GPU'):
        print(f"Found GPU: {gpu}")
else:
    print("❌ No GPU found, using CPU")
print("="*50) 