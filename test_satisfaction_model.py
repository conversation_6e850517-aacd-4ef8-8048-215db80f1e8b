#!/usr/bin/env python3
"""
Customer Satisfaction Model Testing Script

This script provides comprehensive testing functionality for the customer satisfaction
classifier trained on facial expressions. It can test individual images, batches of images,
or real-time camera input.

Usage:
    python test_satisfaction_model.py --image path/to/image.jpg
    python test_satisfaction_model.py --folder path/to/images/
    python test_satisfaction_model.py --camera
"""

import os
import sys
import argparse
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
import tensorflow as tf
from tensorflow.keras.models import load_model
import warnings
warnings.filterwarnings('ignore')

class SatisfactionClassifier:
    """Customer Satisfaction Classifier for facial expressions"""
    
    def __init__(self, model_path='satisfaction_model_best.h5'):
        """
        Initialize the classifier
        
        Args:
            model_path (str): Path to the trained model file
        """
        self.model_path = model_path
        self.model = None
        self.satisfaction_labels = {
            0: 'Satisfied',
            1: 'Neutral', 
            2: 'Unsatisfied'
        }
        self.colors = {
            'Satisfied': (0, 255, 0),      # Green
            'Neutral': (255, 255, 0),      # Yellow
            'Unsatisfied': (0, 0, 255)     # Red
        }
        self.load_model()
        
    def load_model(self):
        """Load the trained model"""
        try:
            if not os.path.exists(self.model_path):
                raise FileNotFoundError(f"Model file not found: {self.model_path}")
            
            print(f"Loading model from {self.model_path}...")
            self.model = load_model(self.model_path)
            print("Model loaded successfully!")
            
        except Exception as e:
            print(f"Error loading model: {e}")
            sys.exit(1)
    
    def preprocess_image(self, image):
        """
        Preprocess image for model prediction
        
        Args:
            image: Input image (numpy array or path)
            
        Returns:
            Preprocessed image ready for model input
        """
        # If image is a path, load it
        if isinstance(image, (str, Path)):
            if not os.path.exists(image):
                raise FileNotFoundError(f"Image file not found: {image}")
            image = cv2.imread(str(image))
            if image is None:
                raise ValueError(f"Could not load image: {image}")
        
        # Convert BGR to RGB if needed
        if len(image.shape) == 3 and image.shape[2] == 3:
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Convert to grayscale (model expects 48x48 grayscale)
        if len(image.shape) == 3:
            image = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # Resize to 48x48 (model input size)
        image = cv2.resize(image, (48, 48))
        
        # Normalize pixel values to [0, 1]
        image = image.astype(np.float32) / 255.0
        
        # Add batch and channel dimensions
        image = np.expand_dims(image, axis=0)  # Add batch dimension
        image = np.expand_dims(image, axis=-1)  # Add channel dimension
        
        return image
    
    def predict_single(self, image, return_probabilities=False):
        """
        Predict satisfaction for a single image
        
        Args:
            image: Input image (numpy array or path)
            return_probabilities (bool): Whether to return class probabilities
            
        Returns:
            Prediction result dictionary
        """
        try:
            # Preprocess image
            processed_image = self.preprocess_image(image)
            
            # Make prediction
            predictions = self.model.predict(processed_image, verbose=0)
            
            # Get predicted class and confidence
            predicted_class = np.argmax(predictions[0])
            confidence = predictions[0][predicted_class]
            predicted_label = self.satisfaction_labels[predicted_class]
            
            result = {
                'predicted_class': predicted_class,
                'predicted_label': predicted_label,
                'confidence': float(confidence),
                'color': self.colors[predicted_label]
            }
            
            if return_probabilities:
                result['probabilities'] = {
                    self.satisfaction_labels[i]: float(predictions[0][i]) 
                    for i in range(len(self.satisfaction_labels))
                }
            
            return result
            
        except Exception as e:
            print(f"Error predicting image: {e}")
            return None
    
    def predict_batch(self, image_paths, show_results=True):
        """
        Predict satisfaction for multiple images
        
        Args:
            image_paths (list): List of image paths
            show_results (bool): Whether to display results
            
        Returns:
            List of prediction results
        """
        results = []
        
        for i, image_path in enumerate(image_paths):
            print(f"Processing image {i+1}/{len(image_paths)}: {image_path}")
            
            result = self.predict_single(image_path, return_probabilities=True)
            if result:
                result['image_path'] = image_path
                results.append(result)
                
                if show_results:
                    print(f"  Prediction: {result['predicted_label']} "
                          f"(Confidence: {result['confidence']:.3f})")
            else:
                print(f"  Failed to process image: {image_path}")
        
        return results
    
    def visualize_prediction(self, image_path, save_path=None):
        """
        Visualize prediction on an image
        
        Args:
            image_path (str): Path to the image
            save_path (str): Optional path to save the result
        """
        try:
            # Load original image
            original_image = cv2.imread(image_path)
            if original_image is None:
                print(f"Could not load image: {image_path}")
                return
            
            original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
            
            # Get prediction
            result = self.predict_single(image_path, return_probabilities=True)
            if not result:
                print(f"Could not predict image: {image_path}")
                return
            
            # Create visualization
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
            
            # Show original image
            ax1.imshow(original_image)
            ax1.set_title(f"Original Image\n{os.path.basename(image_path)}")
            ax1.axis('off')
            
            # Show prediction results
            labels = list(result['probabilities'].keys())
            probabilities = list(result['probabilities'].values())
            colors = ['green' if label == result['predicted_label'] else 'lightblue' 
                     for label in labels]
            
            bars = ax2.bar(labels, probabilities, color=colors)
            ax2.set_title(f"Prediction: {result['predicted_label']}\n"
                         f"Confidence: {result['confidence']:.3f}")
            ax2.set_ylabel('Probability')
            ax2.set_ylim(0, 1)
            
            # Add probability values on bars
            for bar, prob in zip(bars, probabilities):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{prob:.3f}', ha='center', va='bottom')
            
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=150, bbox_inches='tight')
                print(f"Visualization saved to: {save_path}")
            
            plt.show()
            
        except Exception as e:
            print(f"Error visualizing prediction: {e}")
    
    def test_camera(self, camera_id=0):
        """
        Test model with real-time camera input
        
        Args:
            camera_id (int): Camera device ID (usually 0 for default camera)
        """
        try:
            cap = cv2.VideoCapture(camera_id)
            if not cap.isOpened():
                print(f"Could not open camera {camera_id}")
                return
            
            print("Press 'q' to quit, 's' to save current frame")
            frame_count = 0
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    print("Failed to capture frame")
                    break
                
                # Make prediction every few frames to improve performance
                if frame_count % 5 == 0:
                    result = self.predict_single(frame)
                    if result:
                        label = result['predicted_label']
                        confidence = result['confidence']
                        color = result['color']
                        
                        # Draw prediction on frame
                        text = f"{label}: {confidence:.3f}"
                        cv2.putText(frame, text, (10, 30), 
                                   cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)
                
                cv2.imshow('Customer Satisfaction Classifier', frame)
                
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                elif key == ord('s'):
                    # Save current frame
                    filename = f"captured_frame_{frame_count}.jpg"
                    cv2.imwrite(filename, frame)
                    print(f"Frame saved as {filename}")
                
                frame_count += 1
            
            cap.release()
            cv2.destroyAllWindows()
            
        except Exception as e:
            print(f"Error with camera testing: {e}")


def main():
    """Main function to handle command line arguments"""
    parser = argparse.ArgumentParser(description='Test Customer Satisfaction Model')
    parser.add_argument('--model', default='satisfaction_model_best.h5',
                       help='Path to the trained model file')
    parser.add_argument('--image', help='Path to a single image to test')
    parser.add_argument('--folder', help='Path to folder containing images to test')
    parser.add_argument('--camera', action='store_true',
                       help='Test with real-time camera input')
    parser.add_argument('--visualize', action='store_true',
                       help='Show visualization of predictions')
    parser.add_argument('--save-viz', help='Path to save visualization results')
    
    args = parser.parse_args()
    
    # Initialize classifier
    classifier = SatisfactionClassifier(args.model)
    
    if args.image:
        # Test single image
        print(f"Testing single image: {args.image}")
        result = classifier.predict_single(args.image, return_probabilities=True)
        
        if result:
            print(f"\nPrediction Results:")
            print(f"Predicted Label: {result['predicted_label']}")
            print(f"Confidence: {result['confidence']:.3f}")
            print(f"Probabilities:")
            for label, prob in result['probabilities'].items():
                print(f"  {label}: {prob:.3f}")
            
            if args.visualize:
                classifier.visualize_prediction(args.image, args.save_viz)
        else:
            print("Failed to process image")
    
    elif args.folder:
        # Test folder of images
        print(f"Testing images in folder: {args.folder}")
        
        # Get all image files
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        image_paths = []
        
        for ext in image_extensions:
            image_paths.extend(Path(args.folder).glob(f'*{ext}'))
            image_paths.extend(Path(args.folder).glob(f'*{ext.upper()}'))
        
        if not image_paths:
            print(f"No image files found in {args.folder}")
            return
        
        print(f"Found {len(image_paths)} images")
        results = classifier.predict_batch(image_paths)
        
        # Print summary
        print(f"\nSummary of {len(results)} processed images:")
        for label in classifier.satisfaction_labels.values():
            count = sum(1 for r in results if r['predicted_label'] == label)
            print(f"  {label}: {count} images")
    
    elif args.camera:
        # Test with camera
        print("Starting camera test...")
        classifier.test_camera()
    
    else:
        print("Please specify --image, --folder, or --camera")
        parser.print_help()


if __name__ == "__main__":
    main()
