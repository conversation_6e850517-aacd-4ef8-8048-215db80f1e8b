# API Integration for Real-time Satisfaction Detector

This document explains how to use the Session Summaries API integration with the real-time satisfaction detector.

## Overview

The real-time satisfaction detector now supports automatic integration with the Session Summaries API. When configured, the detector will automatically send session data to your API when the session ends.

## New Features

### 1. Automatic API Integration
- Automatically sends session summaries to the API when the detector session ends
- Includes all session statistics: duration, predictions, confidence levels, etc.
- Graceful error handling with fallback to local display

### 2. New Command Line Arguments

```bash
--api-url URL          # API base URL (e.g., http://localhost)
--session-id ID        # Optional session ID for tracking
```

### 3. API Connection Testing
- Automatically tests API connectivity on startup
- Provides clear feedback about API status

## Usage Examples

### Basic Usage with API Integration

```bash
# Run with API integration
python realtime_satisfaction_detector.py --api-url http://localhost

# Run with API and custom session ID
python realtime_satisfaction_detector.py --api-url http://localhost --session-id "store_001_morning"

# Run with API and other options
python realtime_satisfaction_detector.py \
    --api-url http://localhost \
    --session-id "demo_session" \
    --camera 0 \
    --min-face-size 80
```

### Local Mode (No API)

```bash
# Run without API (local display only)
python realtime_satisfaction_detector.py
```

## API Data Format

The detector sends data to the `/api/session-summaries` endpoint with the following format:

```json
{
    "session_duration": 15.5,
    "total_predictions": 10,
    "satisfied_count": 7,
    "neutral_count": 2,
    "unsatisfied_count": 1,
    "average_confidence": 0.85,
    "most_common_prediction": "satisfied",
    "session_id": "optional-session-id"
}
```

## Requirements

### Python Dependencies

The API integration requires the `requests` library:

```bash
pip install requests
```

### API Server

Make sure your Session Summaries API server is running and accessible. The detector will:

1. Test connectivity to `/api/session-summaries/statistics` on startup
2. Send session data to `/api/session-summaries` when the session ends

## Error Handling

The detector includes robust error handling:

- **Connection Errors**: If the API is unreachable, the detector continues normally and displays session data locally
- **API Errors**: If the API returns an error, the detector logs the error and continues
- **Timeout Protection**: API calls have a 10-second timeout to prevent hanging

## Demo Script

Use the included demo script to test the integration:

```bash
python example_api_usage.py
```

This script provides several options:
1. Run with API integration (30-second demo)
2. Run in local mode only
3. Test available cameras
4. Show detector help

## Session Data Mapping

The detector maps its internal data to the API format as follows:

| Detector Data | API Field | Description |
|---------------|-----------|-------------|
| Session duration | `session_duration` | Total session time in seconds |
| Total predictions | `total_predictions` | Number of predictions made |
| Satisfied count | `satisfied_count` | Number of "Satisfied" predictions |
| Neutral count | `neutral_count` | Number of "Neutral" predictions |
| Unsatisfied count | `unsatisfied_count` | Number of "Unsatisfied" predictions |
| Average confidence | `average_confidence` | Mean confidence (0.0-1.0) |
| Most common | `most_common_prediction` | Most frequent prediction type |
| Session ID | `session_id` | Optional session identifier |

## Troubleshooting

### API Connection Issues

1. **Check API Server**: Ensure your API server is running
2. **Check URL**: Verify the API URL is correct (e.g., `http://localhost`)
3. **Check Network**: Ensure network connectivity to the API server
4. **Check Firewall**: Verify firewall settings allow the connection

### Common Error Messages

- `❌ API connection test failed`: API server is not reachable
- `⚠️ API returned status code: XXX`: API server returned an error
- `❌ Error sending data to API`: Network or connection error

### Debug Mode

For debugging, you can check the API response by looking at the console output when the session ends.

## Integration with Existing Systems

The API integration is designed to work seamlessly with existing Session Summaries API implementations. The data format matches the API specification exactly, ensuring compatibility with existing dashboards and analytics systems.
